{"channels": {"email": true, "sms": true, "inapp": true}, "templateId": "drone-alert", "subject": "<PERSON><PERSON> !", "organization": "Aerodefense test", "inAppTopic": "6744bf56520c0c5a5e1c99e1", "phoneNumber": "+12018751131", "email": "<EMAIL>", "data": {"alertZoneName": "alertZoneName", "currentDate": "currentDate", "eventId": "eventId", "uasId": "uasId", "uasType": "uasType", "droneLat": "droneLat", "droneLng": "droneLng1", "pilotLng": "pilotLng2", "locationStatus": "locationStatus", "altitude": "altitude222", "speed": "speed333", "heading": "heading", "operatorType": "operatorType"}}