#!/bin/bash

export PATH=$(brew --prefix kafka)/bin:$PATH

export CLASSPATH='/Users/<USER>/Developer/CoDDN_Notifications/function/aws-msk-iam-auth-2.2.0-all.jar'

kafka-console-consumer \
--bootstrap-server 'b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198' \
--topic $1 \
--consumer.config 'client.properties'
