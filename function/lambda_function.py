import json
import logging
from pymongo import MongoClient, GEOSPHERE
import socket
import json
import datetime
import requests
# from twilioHandler import TwilioHandler
from bson import ObjectId
import os,traceback
import base64
from twilioHandler import TwilioHandler
from appSyncHandler import publish_message

# Load environment variables
MONGODB_URI = os.getenv("MONGODB_CONNECTION_STRING")
MONGODB_DB_NAME = os.getenv("DATABASE_NAME", "coddn")

ACCESS_KEY = os.getenv("ACCESS_KEY")
SECRET_KEY = os.getenv("SECRET_KEY")
REGION = os.getenv("REGION")
BOOTSTRAP_SERVERS = os.getenv("BOOTSTRAP_SERVERS")



def lambda_handler(event, context):
    print('current event is:',event)
    for key in event['records'].keys():
        records = event['records'][key]
        print('current message is:',records)
        for record in records:
            print('current message is:',record)
            value = base64.b64decode(record['value']).decode("utf-8") 
            print("current value is:",value)
            value = json.loads(value)
            print("data is ", value['data'])
            #process_message(context, value)
    return {
        "statusCode": "200",
    }
