OpenDroneID_basicID_uaType={'0':    'None/Not Declared',
							'1':    'Aeroplane',
							'2':    'Helicopter/Multirotor',
							'3':    'Gryroplane',
							'4':    'Hybrid Lift(Fixed wing that can take off vertically)',
							'5':    'Ornithopter',
							'6':    'Glider',
							'7':    'Kit<PERSON>',
							'8':    'Free Balloon',
							'9':    'Captive Balloon',
							'10':   'Airship(Such as a blimp)',
							'11':   'Free Fall/Parachute(unpowered)',
							'12':   'Rocket',
							'13':   'Tethered Poawer AirCraft',
							'14':   'Ground Obstacles',
							'15':   'Other'}
OpenDroneID_loc_status={'0':    'Undeclared',
						'1':    'Ground',
						'2':    'Airborne',
						'3':    'Emergency',
						'4':    'Remote ID System Failure',
						'5':    'Reserved',
						'6':    'Reserved',
						'7':    'Reserved',
						'8':    'Reserved',
						'9':    'Reserved',
						'10':   'Reserved',
						'11':   'Reserved',
						'12':   'Reserved',
						'15':   'Reserved',
						'13':   'Reserved',
						'14':   'Reserved'}
OpenDroneID_operator_type={ '0':    'Takeoff',
							'1':    'Dynamic',
							'2':    'Fixed'}