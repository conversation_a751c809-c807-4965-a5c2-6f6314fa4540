---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-service-qa
  namespace: coddn-qa
  labels:
    app: notification-service-qa
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: notification-service-qa
  template:
    metadata:
      labels:
        app: notification-service-qa
    spec:
      containers:
        - name: notification-service-qa
          image: ************.dkr.ecr.us-east-2.amazonaws.com/qa/notification-service:latest
          ports:
            - containerPort: 3002
          env:
            - name: SENDGRID_API_KEY
              value: *********************************************************************
            - name: ACCOUNT_SID
              value: **********************************
            - name: AUTH_TOKEN
              value: 133e13ef0b2c1ee53efdbb9d21dfdc2b
            - name: MONGODB_CONNECTION_STRING
              value: 'mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=CoddnDev'
            - name: DATABASE_NAME
              value: 'coddnQA'
            - name: GROUP_ID
              value: qaGroupNotification
            - name: NOTIFICATION_TOPIC
              value: NOTIFICATION_QA
            - name: RID_TOPIC
              value: RID_QA
            - name: ACCESS_KEY
              value: ********************
            - name: SECRET_KEY
              value: XB5g7U5NBtDCzd5vTXoJLumcDEuzA8sOulVO2tIg
            - name: REGION
              value: us-east-2
            - name: BOOTSTRAP_SERVERS
              value: 'b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198'
            - name: APP_SYNC_URL
              value: https://3ehud5ztijb4rdwy3l3f4iblba.appsync-api.us-east-2.amazonaws.com/graphql
            - name: APP_SYNC_API_KEY
              value: da2-ylyaaxohxre2bfthzifptrsnw4
            - name: ENV_URL
              value: https://qa.aerodefense.tech
