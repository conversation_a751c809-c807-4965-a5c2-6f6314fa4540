---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-service
  namespace: prod
  labels:
    app: notification-service
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: notification-service
  template:
    metadata:
      labels:
        app: notification-service
    spec:
      containers:
        - name: notification-service
          image: ************.dkr.ecr.us-east-2.amazonaws.com/prod/notification-service:latest
          ports:
            - containerPort: 3001
          env:
            - name: SENDGRID_API_KEY
              value: *********************************************************************
            - name: ACCOUNT_SID
              value: **********************************
            - name: AUTH_TOKEN
              value: 133e13ef0b2c1ee53efdbb9d21dfdc2b
            - name: MONGODB_CONNECTION_STRING
              value: 'mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=AirWardenEssentials'
            - name: DATABASE_NAME
              value: 'coddn'
            - name: GROUP_ID
              value: prodGroupNotification
            - name: NOTIFICATION_TOPIC
              value: NOTIFICATION
            - name: RID_TOPIC
              value: RID
            - name: ACCESS_KEY
              value: ********************
            - name: SECRET_KEY
              value: t3CNjbhxVIcPwYJbTnfXReG083LxV6yCJSsBASxn
            - name: REGION
              value: us-east-2
            - name: BOOTSTRAP_SERVERS
              value: 'b-1-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198,b-2-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198'
            - name: APP_SYNC_URL
              value: https://nzf6alzxl5grfhylui4srzpfka.appsync-api.us-east-2.amazonaws.com/graphql
            - name: APP_SYNC_API_KEY
              value: da2-vcdnqfsqtjgbflxmr3rnhxchba
            - name: ENV_URL
              value: https://app.aerodefense.tech
