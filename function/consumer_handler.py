# from confluent_kafka import Consumer
from confluent_kafka import Consumer
from aws_msk_iam_sasl_signer import MSKAuthTokenProvider
from botocore.credentials import CredentialProvider, Credentials
import socket
import os
import json
from NotificationService import NotificaitonService 
import asyncio

ACCESS_KEY = os.getenv("ACCESS_KEY", "********************")
SECRET_KEY = os.getenv("SECRET_KEY", "4XFYdQrDYfxDScDczKgTvs/IETy/Amkf/My4pXjM")
REGION = os.getenv("REGION", "us-east-2")
BOOTSTRAP_SERVERS = os.getenv("BOOTSTRAP_SERVERS","b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198")
NOTIFICATION_TOPIC = os.getenv("NOTIFICATION_TOPIC", "NOTIFICATION_DEV")
class TestCredentialProvider(CredentialProvider):
	__test__ = False

	def load(self):
		return Credentials(
			access_key=ACCESS_KEY,
			secret_key=SECRET_KEY,
		)
	
def oauth_cb(oauth_config):
	auth_token, expiry_ms = MSKAuthTokenProvider.generate_auth_token_from_credentials_provider(REGION, TestCredentialProvider())
	#print(auth_token)
	# Note that this library expects oauth_cb to return expiry time in seconds since epoch, while the token generator returns expiry in ms
	return auth_token, expiry_ms/1000


class NotificationCenterHandler():
	notificationService = NotificaitonService()
	# tp = MSKTokenProvider()

	def main(self):
		consumer = Consumer({
				# "debug": "all",
				'bootstrap.servers': BOOTSTRAP_SERVERS,
				'client.id': socket.gethostname(),
				'security.protocol': 'SASL_SSL',
				'sasl.mechanisms': 'OAUTHBEARER',
				'oauth_cb': oauth_cb,
				'group.id':  os.getenv('GROUP_ID',"notification-tesasdasdsadasda"),
				'auto.offset.reset': 'latest',
			})
		consumer.subscribe([NOTIFICATION_TOPIC])

		while True:
			msg = consumer.poll(5)

			if msg is None:
				continue
			if msg.error():
				print("Consumer error: {}".format(msg.error()))
				continue
			msg = msg.value().decode('utf-8')
			print('Received message: {}'.format(msg))
			asyncio.run(self.notificationService.process_message(json.loads(msg)))


if __name__ == '__main__':
	print("Starting notification service !!")
	notify = NotificationCenterHandler()
	notify.main()
