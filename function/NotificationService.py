import json
import os,traceback
import base64
from twilioHandler import <PERSON><PERSON>lioH<PERSON><PERSON>
from appSyncHandler import publish_message
import asyncio

# Load environment variables
MONGODB_URI = os.getenv("MONGODB_CONNECTION_STRING")
MONGODB_DB_NAME = os.getenv("DATABASE_NAME", "coddn")

ACCESS_KEY = os.getenv("ACCESS_KEY")
SECRET_KEY = os.getenv("SECRET_KEY")
REGION = os.getenv("REGION")
BOOTSTRAP_SERVERS = os.getenv("BOOTSTRAP_SERVERS")

class NotificaitonService:
    async def process_message(self,message):
        try:
            twilioService = TwilioHandler()

            if(message['channels']['email'] == True):
                try:
                    print("Sending email to ", message['email'] )
                    await twilioService.send_email(message['email'], message['subject'], message['templateId'], message['data'])
                except:
                    print("Failed to send email to ", message['email'])
                    traceback.print_exc()
            if(message['channels']['sms'] == True):
                try:
                    print("Sending email to ", message['phoneNumber'] )
                    await twilioService.send_sms(message['phoneNumber'], message['subject'], message['templateId'], message['data'])
                except:
                    print("Failed to send sms to ", message['phoneNumber'])
                    traceback.print_exc()
            if(message['channels']['inapp'] == True):
                try:
                    print("to be implemented later")
                #    await publish_message(message['inAppTopic'], message['data']))
                except:
                    traceback.print_exc()

            return message
        except:
            traceback.print_exc()