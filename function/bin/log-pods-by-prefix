#!/bin/bash

# This script lists all pods in a specified Kubernetes namespace.

# check if namespace is provided
if [ -z "$1" ]; then
  echo "Namespace is not provided"
  exit 1
fi

if [ -z "$2" ]; then
  echo "AppName is not provided"
  exit 1
fi

PODS=$(kubectl get pods -n $1 -o jsonpath='{.items[*].metadata.name}')

# Loop through each pod and open a new terminal for its logs
for POD in $PODS; do
  if [[ "$POD" == $2* ]]; then
  # Open new VS Code terminal (Cmd + Shift + `)
  osascript -e 'tell application "System Events" to keystroke "`" using {control down, shift down}'
  sleep 0.5  # Small delay to ensure terminal opens
  
  # Type the log command
  osascript -e "tell application \"System Events\" to keystroke \"kubectl logs -f $POD -n $1 \""
  
  # Press Enter
  osascript -e "tell application \"System Events\" to key code 36"
  fi
done