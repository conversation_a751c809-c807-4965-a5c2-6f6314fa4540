from twilio.rest import Client
from sendgrid import SendGridAP<PERSON>lient
from sendgrid.helpers.mail import Mail
from jinja2 import Environment, FileSystemLoader
import os


ENV_URL = os.getenv("ENV_URL")
ACCOUNT_SID = os.getenv("ACCOUNT_SID")
AUTH_TOKEN = os.getenv("AUTH_TOKEN")
SENDGRID_API_KEY = os.getenv("SENDGRID_API_KEY")


class TwilioHandler:
	from_phone = '+***********'
	email_user = '<EMAIL>'

	def __init__(self):
		print("Account SID:", ACCOUNT_SID, "Auth Token:", AUTH_TOKEN)
		self.client = Client(ACCOUNT_SID, AUTH_TOKEN)
		# Initialize Jinja2 environment
		self.jinja_env = Environment(loader=FileSystemLoader('templates'))

	async def send_sms(self, to_phone, subject, templateId, data):
		with open(f"templates/{templateId}.txt", "r", encoding="utf-8") as file:
			message_body = file.read()  # Reads the whole file into a string
		message_body = message_body.format(subject=subject, **data)
		print(message_body)
		try:
			print("sending sms...", message_body)
			message = self.client.messages.create(
				body=message_body,
				from_=self.from_phone,
				to=to_phone
			)
			print("SMS sent to:", to_phone)
		except Exception as e:
			print("Invalid number:", to_phone)

	def render_template(self, template_name, **kwargs):
		# Load the template and render it with provided variables
		template = self.jinja_env.get_template(template_name)
		return template.render(**kwargs)


	async def send_email(self, to_user_email, subject, template_name, data):
		# Render the HTML content using the Jinja2 template
		body_html = self.render_template(f'{template_name}.html', **data, env_url=ENV_URL)
		
		# Create SendGrid email message
		message = Mail(
			from_email=self.email_user,
			to_emails=to_user_email,
			subject=subject,
			html_content=body_html)
		try:
			print("Sending email...")
			sg = SendGridAPIClient(SENDGRID_API_KEY)
			response = sg.send(message)
			print("Email sent to:", to_user_email, "Response:", response.status_code)
		except Exception as e:
			print(f"Failed to send email: {e}")

