version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 399444019738.dkr.ecr.us-east-2.amazonaws.com
      - echo Configuring AWS CLI
      - aws configure set aws_access_key_id ********************
      - aws configure set aws_secret_access_key Rs02avH9Y5tPMIacyLV9lrH8hjdwF1sj7Y85j7ND
      - aws configure set region us-east-2
      - aws eks update-kubeconfig --region us-east-2 --name coddn
      - docker login --username alexmedina443 --password ************************************
  build:
    commands:
      - cd function
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build --build-arg ENVIRONMENT=qa -t notification-service .
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker image to ECR...
      - echo Build completed on `date`
      - IMAGE_TAG=$(date +%Y%m%d%H%M%S)
      - echo Pushing the Docker image to ECR with tag $IMAGE_TAG...
      - docker tag notification-service:latest 399444019738.dkr.ecr.us-east-2.amazonaws.com/qa/notification-service:$IMAGE_TAG
      - docker push 399444019738.dkr.ecr.us-east-2.amazonaws.com/qa/notification-service:$IMAGE_TAG
      - kubectl apply -f yaml-files/deployment-qa.yaml
      - kubectl set image deployment/notification-service-qa notification-service-qa=399444019738.dkr.ecr.us-east-2.amazonaws.com/qa/notification-service:$IMAGE_TAG -n coddn-qa
artifacts:
  files:
    - '**/*'
  discard-paths: yes