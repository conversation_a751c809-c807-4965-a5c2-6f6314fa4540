# CoDDN Notifications Service

## Overview

The CoDDN Notifications Service is a comprehensive notification system designed for drone detection and alert management. It's part of the AeroDefense ecosystem and handles multi-channel notifications including email, SMS, and in-app notifications for drone detection events.

## Architecture

The service is built using Python and follows a microservices architecture with the following key components:

- **Kafka Consumer**: Consumes notification messages from AWS MSK (Managed Streaming for Apache Kafka)
- **Notification Service**: Processes messages and routes them to appropriate channels
- **Twilio Handler**: Manages SMS and email notifications via Twilio and SendGrid
- **AppSync Handler**: Handles in-app notifications via AWS AppSync GraphQL API
- **Template Engine**: Uses Jinja2 for dynamic content generation

## Technology Stack

- **Language**: Python 3.13
- **Message Broker**: Apache Kafka (AWS MSK)
- **Database**: MongoDB
- **Email Service**: SendGrid
- **SMS Service**: Twilio
- **Real-time Notifications**: AWS AppSync
- **Container Platform**: Docker
- **Orchestration**: Kubernetes (AWS EKS)
- **CI/CD**: AWS CodeBuild
- **Container Registry**: Amazon ECR

## Project Structure

```
CoDDN_Notifications/
├── function/                    # Main application code
│   ├── consumer_handler.py      # Kafka consumer implementation
│   ├── NotificationService.py   # Core notification processing logic
│   ├── twilioHandler.py         # SMS and email handling
│   ├── appSyncHandler.py        # In-app notification handling
│   ├── lambda_function.py       # AWS Lambda entry point
│   ├── requirements.txt         # Python dependencies
│   ├── dockerfile              # Docker container configuration
│   ├── templates/              # Notification templates
│   │   ├── drone-alert.html    # HTML email template
│   │   ├── drone-alert.txt     # SMS text template
│   │   └── ...                 # Other templates
│   ├── yaml-files/             # Kubernetes deployment configs
│   └── bin/                    # Utility scripts
├── build-files/                # CI/CD build specifications
│   ├── buildspec-dev.yaml      # Development environment build
│   ├── buildspec-staging.yaml  # Staging environment build
│   ├── buildspec-prod.yaml     # Production environment build
│   └── buildspec-qa.yaml       # QA environment build
├── layer/                      # AWS Lambda layer dependencies
├── testing/                    # Testing utilities and scripts
└── README.md                   # Basic project information
```

## Features

### Multi-Channel Notifications
- **Email**: Rich HTML templates with drone detection details
- **SMS**: Text-based alerts with essential information
- **In-App**: Real-time notifications via GraphQL subscriptions

### Template System
- Dynamic content generation using Jinja2
- Customizable templates for different alert types
- Support for multiple notification formats

### Environment Support
- Development, QA, Staging, and Production environments
- Environment-specific configurations and resources
- Isolated Kafka topics and databases per environment

### Scalability
- Kubernetes-based deployment with configurable replicas
- Kafka consumer groups for load distribution
- Docker containerization for consistent deployments

## Prerequisites

Before running this project, ensure you have the following installed and configured:

### Required Software
- Python 3.13+
- Docker
- kubectl (for Kubernetes deployments)
- AWS CLI (configured with appropriate credentials)

### Required Services
- AWS Account with access to:
  - Amazon MSK (Managed Streaming for Apache Kafka)
  - Amazon EKS (Elastic Kubernetes Service)
  - Amazon ECR (Elastic Container Registry)
  - AWS AppSync
- MongoDB Atlas or MongoDB instance
- Twilio account (for SMS)
- SendGrid account (for email)

### Environment Variables

The following environment variables need to be configured:

```bash
# AWS Configuration
ACCESS_KEY=your_aws_access_key
SECRET_KEY=your_aws_secret_key
REGION=us-east-2
BOOTSTRAP_SERVERS=your_kafka_bootstrap_servers

# Database
MONGODB_CONNECTION_STRING=your_mongodb_connection_string
DATABASE_NAME=coddn

# Notification Services
SENDGRID_API_KEY=your_sendgrid_api_key
ACCOUNT_SID=your_twilio_account_sid
AUTH_TOKEN=your_twilio_auth_token

# Kafka Configuration
GROUP_ID=your_consumer_group_id
NOTIFICATION_TOPIC=your_notification_topic

# AppSync Configuration
APP_SYNC_URL=your_appsync_graphql_endpoint
APP_SYNC_API_KEY=your_appsync_api_key

# Application Configuration
ENV_URL=your_application_frontend_url
```

## Installation and Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd CoDDN_Notifications
```

### 2. Install Python Dependencies
```bash
cd function
pip install -r requirements.txt
```

### 3. Configure Environment Variables
Create a `.env` file or set environment variables as shown in the prerequisites section.

### 4. Set Up Kafka Client Properties
Configure the `testing/client.properties` file for Kafka authentication:
```properties
security.protocol=SASL_SSL
sasl.mechanism=AWS_MSK_IAM
sasl.jaas.config=software.amazon.msk.auth.iam.IAMLoginModule required;
sasl.client.callback.handler.class=software.amazon.msk.auth.iam.IAMClientCallbackHandler
```

## Running the Application

### Local Development

#### Option 1: Direct Python Execution
```bash
cd function
python consumer_handler.py
```

#### Option 2: Docker Container
```bash
cd function
docker build -t notification-service .
docker run -e ACCESS_KEY=your_key -e SECRET_KEY=your_secret notification-service
```

### Testing the Service

#### 1. Test Kafka Message Production
```bash
cd testing
# Make the script executable
chmod +x produce-message

# Send test messages
./produce-message
```

#### 2. Test Kafka Message Consumption
```bash
cd testing
# Make the script executable
chmod +x consumer-test

# Consume messages from a topic
./consumer-test NOTIFICATION_DEV
```

#### 3. Sample Test Message
The service expects messages in the following format:
```json
{
  "channels": {
    "email": true,
    "sms": true,
    "inapp": true
  },
  "templateId": "drone-alert",
  "subject": "Drone Detection Alert",
  "organization": "Your Organization",
  "inAppTopic": "topic_id",
  "phoneNumber": "+1234567890",
  "email": "<EMAIL>",
  "data": {
    "alertZoneName": "Zone Name",
    "currentDate": "2024-01-01",
    "eventId": "event123",
    "uasId": "drone456",
    "uasType": "Quadcopter",
    "droneLat": "40.7128",
    "droneLng": "-74.0060",
    "pilotLat": "40.7130",
    "pilotLng": "-74.0062",
    "locationStatus": "Active",
    "altitude": "100",
    "speed": "15",
    "heading": "North",
    "operatorType": "Unknown"
  }
}
```

## Deployment

### AWS Lambda Deployment

#### 1. Create Lambda Layer
```bash
cd layer
chmod +x package
./package
```

#### 2. Update Lambda Function Code
```bash
cd function/bin
chmod +x update-code
./update-code
```

### Kubernetes Deployment

#### 1. Build and Push Docker Image
The CI/CD pipeline automatically builds and pushes images, but for manual deployment:

```bash
# Development
aws codebuild start-build --project-name notification-service-dev

# Staging  
aws codebuild start-build --project-name notification-service-staging

# Production
aws codebuild start-build --project-name notification-service-prod
```

#### 2. Manual Kubernetes Deployment
```bash
cd function
# Apply deployment configuration
kubectl apply -f yaml-files/deployment-development.yaml

# Check deployment status
kubectl get pods -n development
```

### Environment-Specific Deployments

Each environment has its own configuration:

- **Development**: `deployment-development.yaml` - Single replica, development resources
- **QA**: `deployment-qa.yaml` - Single replica, QA resources  
- **Staging**: `deployment-staging.yaml` - 5 replicas, staging resources
- **Production**: `deployment-prod.yaml` - Single replica, production resources

## Monitoring and Troubleshooting

### Logs
```bash
# View application logs
kubectl logs -f deployment/notification-service-development -n development

# View specific pod logs
kubectl logs -f <pod-name> -n <namespace>
```

### Health Checks
The service runs on port 3001 (3002 for QA) and can be monitored through Kubernetes health checks.

### Common Issues

1. **Kafka Connection Issues**: Verify AWS MSK cluster accessibility and IAM permissions
2. **Template Not Found**: Ensure template files exist in the `templates/` directory
3. **SendGrid/Twilio Failures**: Check API keys and account limits
4. **MongoDB Connection**: Verify connection string and network access

## Contributing

1. Follow the existing code structure and naming conventions
2. Add appropriate error handling and logging
3. Update templates when adding new notification types
4. Test changes in development environment before promoting
5. Update documentation for any new features or changes

## Security Considerations

- All sensitive credentials are stored as environment variables
- Kafka uses IAM-based authentication with AWS MSK
- MongoDB connections use encrypted connections
- API keys for external services are environment-specific

## Support

For issues and questions:
1. Check the logs for error messages
2. Verify environment configuration
3. Test individual components (Kafka, MongoDB, external APIs)
4. Review the troubleshooting section above
