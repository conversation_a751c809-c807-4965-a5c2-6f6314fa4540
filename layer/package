#!/bin/bash

## install the requirements
python3.13 -m venv create_layer
source create_layer/bin/activate
pip install -r requirements.txt

## package the dependencies
mkdir python
cp -r create_layer/lib python/
zip -r layer_content.zip python

## publish the layer 
aws lambda publish-layer-version --layer-name notification-service-dependencies \
    --zip-file fileb://layer_content.zip \
    --compatible-runtimes python3.13 \
    --compatible-architectures "x86_64" "arm64"

## clean up
rm -rf create_layer
rm -rf python
rm -rf layer_content.zip